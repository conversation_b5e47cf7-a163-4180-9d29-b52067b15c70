import assert from "assert";
import docopt from "docopt";

import { SequentBatchEventBus } from "../src/domain/EventBus.js";
import PullTmdbContentJob, {
  EventBusLogger,
} from "../src/jobs/PullTmdbContentJob.js";
import ConnectionPool from "../src/postgres/ConnectionPool.js";
import PostgresEventHandler from "../src/postgres/PostgresEventHandler.js";
import PostgresTmdbGenreRepository from "../src/postgres/PostgresTmdbGenreRepository.js";
import PostgresTmdbImageStorage from "../src/postgres/PostgresTmdbImageStorage.js";
import PostgresTmdbJobQueue from "../src/postgres/PostgresTmdbJobQueue.js";
import PostgresTmdbKeywordRepository from "../src/postgres/PostgresTmdbKeywordRepository.js";
import PostgresTmdbMovieRepository from "../src/postgres/PostgresTmdbMovieRepository.js";
import PostgresTmdbPersonRepository from "../src/postgres/PostgresTmdbPersonRepository.js";
import PostgresTmdbQueryHandle from "../src/postgres/PostgresTmdbQueryHandle.js";
import HttpTmdbGateway from "../src/tmdb/HttpTmdbGateway.js";

docopt.docopt(
  `
TMDb

Usage:
  tmdb

Options:
  -h --help    Show this screen.
`.trim(),
  { argv: process.argv.slice(2) },
) as CliOptions;

async function runScript(): Promise<void> {
  assert(
    process.env.POSTGRES_CONNECTION_STRING,
    "Missing POSTGRES_CONNECTION_STRING env variable",
  );
  assert(process.env.TMDB_API_KEY, "Missing TMDB_API_KEY env variable");
  assert(process.env.IMAGES_PATH, "Missing IMAGES_PATH env variable");

  const pool = new ConnectionPool(process.env.POSTGRES_CONNECTION_STRING);
  const imageStorage = new PostgresTmdbImageStorage(
    pool,
    process.env.IMAGES_PATH,
  );
  const movieRepository = new PostgresTmdbMovieRepository(pool);
  const genreRepository = new PostgresTmdbGenreRepository(pool);
  const keywordRepository = new PostgresTmdbKeywordRepository(pool);
  const personRepository = new PostgresTmdbPersonRepository(pool);
  const queue = new PostgresTmdbJobQueue(pool);
  const queryHandle = new PostgresTmdbQueryHandle(pool);
  const tmdbGateway = new HttpTmdbGateway(process.env.TMDB_API_KEY, debug);
  const eventBus = new SequentBatchEventBus([
    new PostgresEventHandler(debug, pool),
    new EventBusLogger(process.stdout),
  ]);
  const job = new PullTmdbContentJob(
    eventBus,
    genreRepository,
    imageStorage,
    keywordRepository,
    movieRepository,
    personRepository,
    tmdbGateway,
    queue,
    queryHandle,
    debug,
  );

  debug("TMDB");
  await job.run();
  await eventBus.batch();

  debug("Done");
  process.exit(0);
}

runScript().catch((error: unknown) => {
  debug(error instanceof Error ? error.stack ?? error.message : String(error));
  process.exit(1);
});

function debug(message: string): void {
  process.stderr.write(
    `[${new Date().toTimeString().slice(0, 8)}] ${message}\n`,
  );
}

type CliOptions = unknown;
