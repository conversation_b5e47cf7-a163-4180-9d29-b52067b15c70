import { QueryHandle } from "../jobs/PullTmdbContentJob.js";
import ConnectionPool from "./ConnectionPool.js";
import sql from "./sql.js";

export default class PostgresTmdbQueryHandle implements QueryHandle {
  constructor(private pool: ConnectionPool) {}

  async getRandomMovieIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_movie
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }

  async getRandomPersonIds(limit: number): Promise<string[]> {
    return this.pool.transaction(async (connection) => {
      const rows = await connection.query<{ id: number }>(sql`
        SELECT id
        FROM tmdb_person
        ORDER BY RANDOM()
        LIMIT ${limit}
      `);
      return rows.map((row) => String(row.id));
    });
  }
}
